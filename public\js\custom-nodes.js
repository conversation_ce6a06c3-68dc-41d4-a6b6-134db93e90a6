// 全局变量存储自定义节点
let customNodes = [];

// 初始化自定义节点功能
function initCustomNodesFeature() {
    console.log('开始初始化自定义节点功能...');

    // 添加自定义节点按钮事件监听器
    document.querySelectorAll('.dropdown-item[data-node-type]').forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            const nodeType = e.target.closest('[data-node-type]').getAttribute('data-node-type');
            console.log(`添加 ${nodeType} 节点`);
            addCustomNode(nodeType);
        });
    });

    // 保存自定义节点按钮事件监听器
    const saveCustomNodesBtn = document.getElementById('save-custom-nodes-btn');
    if (saveCustomNodesBtn) {
        saveCustomNodesBtn.addEventListener('click', async () => {
            const success = await saveAllConfig();
            if (success) {
                showStatus('自定义节点已保存', 'success');
                updateCustomNodesResult();
            }
        });
    }

    // 渲染现有的自定义节点
    renderCustomNodes();

    console.log('自定义节点功能初始化完成');
}

// 添加自定义节点
function addCustomNode(type) {
    const nodeId = Date.now().toString();
    const node = {
        id: nodeId,
        type: type,
        enabled: true,
        customName: '',
        config: getDefaultNodeConfig(type)
    };

    customNodes.push(node);
    console.log(`添加了新的 ${type} 节点:`, node);
    renderCustomNodes();
    saveAllConfig(); // 自动保存
}

// 获取默认节点配置
function getDefaultNodeConfig(type) {
    switch (type) {
        case 'trojan':
            return {
                server: '',
                port: 443,
                password: '',
                sni: '',
                'skip-cert-verify': false,
                udp: true
            };
        case 'ss':
            return {
                server: '',
                port: 443,
                cipher: 'aes-256-gcm',
                password: '',
                udp: true
            };
        case 'vmess':
            return {
                server: '',
                port: 443,
                uuid: '',
                alterId: 0,
                cipher: 'auto',
                network: 'tcp',
                tls: true,
                'skip-cert-verify': false,
                udp: true,
                'ws-opts': {
                    path: '/',
                    headers: {
                        Host: ''
                    }
                }
            };
        default:
            return {};
    }
}

// 渲染自定义节点列表
function renderCustomNodes() {
    const container = document.getElementById('custom-nodes-list');
    if (!container) {
        console.warn('自定义节点容器未找到');
        return;
    }

    console.log(`渲染 ${customNodes.length} 个自定义节点`);
    container.innerHTML = '';

    customNodes.forEach((node, index) => {
        const nodeElement = createCustomNodeElement(node, index);
        container.appendChild(nodeElement);
    });

    updateCustomNodesResult();
}

// 创建自定义节点元素
function createCustomNodeElement(node, index) {
    const div = document.createElement('div');
    div.className = 'custom-node-item border rounded p-3 mb-3';
    div.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h6 class="mb-0">
                <i class="bi bi-${getNodeIcon(node.type)}"></i>
                ${getNodeTypeName(node.type)} 节点 #${index + 1}
            </h6>
            <div class="d-flex align-items-center gap-2">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="node-enabled-${node.id}"
                           ${node.enabled ? 'checked' : ''} onchange="toggleCustomNode('${node.id}')">
                    <label class="form-check-label" for="node-enabled-${node.id}">启用</label>
                </div>
                <button class="btn btn-outline-danger btn-sm" onclick="removeCustomNode('${node.id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <label class="form-label">自定义名称 (可选)</label>
                <input type="text" class="form-control" placeholder="留空则自动生成"
                       value="${node.customName || ''}" onchange="updateCustomNodeName('${node.id}', this.value)">
            </div>
        </div>

        ${createNodeConfigForm(node)}
    `;
    return div;
}

// 获取节点图标
function getNodeIcon(type) {
    switch (type) {
        case 'trojan': return 'shield-lock';
        case 'ss': return 'eye-slash';
        case 'vmess': return 'lightning';
        default: return 'gear';
    }
}

// 获取节点类型名称
function getNodeTypeName(type) {
    switch (type) {
        case 'trojan': return 'Trojan';
        case 'ss': return 'Shadowsocks';
        case 'vmess': return 'VMess';
        default: return '未知';
    }
}

// 切换自定义节点启用状态
function toggleCustomNode(nodeId) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node) {
        node.enabled = !node.enabled;
        saveAllConfig(); // 自动保存
        updateCustomNodesResult();
    }
}

// 移除自定义节点
function removeCustomNode(nodeId) {
    const index = customNodes.findIndex(n => n.id === nodeId);
    if (index !== -1) {
        customNodes.splice(index, 1);
        renderCustomNodes();
        saveAllConfig(); // 自动保存
    }
}

// 更新自定义节点名称
function updateCustomNodeName(nodeId, name) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node) {
        node.customName = name.trim();
        saveAllConfig(); // 自动保存
        updateCustomNodesResult();
    }
}

// 更新节点配置
function updateNodeConfig(nodeId, key, value) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node) {
        node.config[key] = value;
        saveAllConfig(); // 自动保存

        // 如果是VMess节点且网络类型改变，重新渲染
        if (node.type === 'vmess' && key === 'network') {
            renderCustomNodes();
        }
    }
}

// 更新WebSocket配置
function updateWSConfig(nodeId, key, value) {
    const node = customNodes.find(n => n.id === nodeId);
    if (node && node.config['ws-opts']) {
        if (key === 'path') {
            node.config['ws-opts'].path = value;
        } else if (key === 'host') {
            if (!node.config['ws-opts'].headers) {
                node.config['ws-opts'].headers = {};
            }
            node.config['ws-opts'].headers.Host = value;
        }
        saveAllConfig(); // 自动保存
    }
}

// 更新自定义节点结果显示
function updateCustomNodesResult() {
    const resultContainer = document.getElementById('custom-nodes-result');
    const resultContent = document.getElementById('custom-nodes-result-content');

    if (!resultContainer || !resultContent) return;

    const enabledNodes = customNodes.filter(node => node.enabled);
    const totalNodes = customNodes.length;

    if (totalNodes === 0) {
        resultContainer.classList.add('d-none');
        return;
    }

    resultContainer.classList.remove('d-none');
    resultContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <strong>总节点数:</strong> ${totalNodes}
            </div>
            <div class="col-md-6">
                <strong>启用节点数:</strong> ${enabledNodes.length}
            </div>
        </div>
    `;
}

// 创建节点配置表单
function createNodeConfigForm(node) {
    switch (node.type) {
        case 'trojan':
            return `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">服务器地址 *</label>
                        <input type="text" class="form-control" value="${node.config.server || ''}"
                               onchange="updateNodeConfig('${node.id}', 'server', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">端口 *</label>
                        <input type="number" class="form-control" value="${node.config.port || 443}"
                               onchange="updateNodeConfig('${node.id}', 'port', parseInt(this.value))" min="1" max="65535">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">密码 *</label>
                        <input type="text" class="form-control" value="${node.config.password || ''}"
                               onchange="updateNodeConfig('${node.id}', 'password', this.value)" placeholder="password">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">SNI (可选)</label>
                        <input type="text" class="form-control" value="${node.config.sni || ''}"
                               onchange="updateNodeConfig('${node.id}', 'sni', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip-cert-${node.id}"
                                   ${node.config['skip-cert-verify'] ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'skip-cert-verify', this.checked)">
                            <label class="form-check-label" for="skip-cert-${node.id}">跳过证书验证</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="udp-${node.id}"
                                   ${node.config.udp ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'udp', this.checked)">
                            <label class="form-check-label" for="udp-${node.id}">UDP 支持</label>
                        </div>
                    </div>
                </div>
            `;
        case 'ss':
            return `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">服务器地址 *</label>
                        <input type="text" class="form-control" value="${node.config.server || ''}"
                               onchange="updateNodeConfig('${node.id}', 'server', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">端口 *</label>
                        <input type="number" class="form-control" value="${node.config.port || 443}"
                               onchange="updateNodeConfig('${node.id}', 'port', parseInt(this.value))" min="1" max="65535">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">加密方式</label>
                        <select class="form-control" onchange="updateNodeConfig('${node.id}', 'cipher', this.value)">
                            <option value="aes-256-gcm" ${node.config.cipher === 'aes-256-gcm' ? 'selected' : ''}>aes-256-gcm</option>
                            <option value="aes-128-gcm" ${node.config.cipher === 'aes-128-gcm' ? 'selected' : ''}>aes-128-gcm</option>
                            <option value="chacha20-poly1305" ${node.config.cipher === 'chacha20-poly1305' ? 'selected' : ''}>chacha20-poly1305</option>
                            <option value="aes-256-cfb" ${node.config.cipher === 'aes-256-cfb' ? 'selected' : ''}>aes-256-cfb</option>
                            <option value="aes-128-cfb" ${node.config.cipher === 'aes-128-cfb' ? 'selected' : ''}>aes-128-cfb</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">密码 *</label>
                        <input type="text" class="form-control" value="${node.config.password || ''}"
                               onchange="updateNodeConfig('${node.id}', 'password', this.value)" placeholder="password">
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="udp-${node.id}"
                                   ${node.config.udp ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'udp', this.checked)">
                            <label class="form-check-label" for="udp-${node.id}">UDP 支持</label>
                        </div>
                    </div>
                </div>
            `;
        case 'vmess':
            return `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">服务器地址 *</label>
                        <input type="text" class="form-control" value="${node.config.server || ''}"
                               onchange="updateNodeConfig('${node.id}', 'server', this.value)" placeholder="example.com">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">端口 *</label>
                        <input type="number" class="form-control" value="${node.config.port || 443}"
                               onchange="updateNodeConfig('${node.id}', 'port', parseInt(this.value))" min="1" max="65535">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">UUID *</label>
                        <input type="text" class="form-control" value="${node.config.uuid || ''}"
                               onchange="updateNodeConfig('${node.id}', 'uuid', this.value)" placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Alter ID</label>
                        <input type="number" class="form-control" value="${node.config.alterId || 0}"
                               onchange="updateNodeConfig('${node.id}', 'alterId', parseInt(this.value))" min="0" max="255">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">加密方式</label>
                        <select class="form-control" onchange="updateNodeConfig('${node.id}', 'cipher', this.value)">
                            <option value="auto" ${node.config.cipher === 'auto' ? 'selected' : ''}>auto</option>
                            <option value="aes-128-gcm" ${node.config.cipher === 'aes-128-gcm' ? 'selected' : ''}>aes-128-gcm</option>
                            <option value="chacha20-poly1305" ${node.config.cipher === 'chacha20-poly1305' ? 'selected' : ''}>chacha20-poly1305</option>
                            <option value="none" ${node.config.cipher === 'none' ? 'selected' : ''}>none</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">网络类型</label>
                        <select class="form-control" onchange="updateNodeConfig('${node.id}', 'network', this.value)">
                            <option value="tcp" ${node.config.network === 'tcp' ? 'selected' : ''}>TCP</option>
                            <option value="ws" ${node.config.network === 'ws' ? 'selected' : ''}>WebSocket</option>
                            <option value="h2" ${node.config.network === 'h2' ? 'selected' : ''}>HTTP/2</option>
                            <option value="grpc" ${node.config.network === 'grpc' ? 'selected' : ''}>gRPC</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="tls-${node.id}"
                                   ${node.config.tls ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'tls', this.checked)">
                            <label class="form-check-label" for="tls-${node.id}">启用 TLS</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip-cert-${node.id}"
                                   ${node.config['skip-cert-verify'] ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'skip-cert-verify', this.checked)">
                            <label class="form-check-label" for="skip-cert-${node.id}">跳过证书验证</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="udp-${node.id}"
                                   ${node.config.udp ? 'checked' : ''}
                                   onchange="updateNodeConfig('${node.id}', 'udp', this.checked)">
                            <label class="form-check-label" for="udp-${node.id}">UDP 支持</label>
                        </div>
                    </div>
                    ${node.config.network === 'ws' ? `
                    <div class="col-12 mb-3">
                        <h6>WebSocket 配置</h6>
                        <div class="row">
                            <div class="col-md-6 mb-2">
                                <label class="form-label">路径</label>
                                <input type="text" class="form-control" value="${node.config['ws-opts']?.path || '/'}"
                                       onchange="updateWSConfig('${node.id}', 'path', this.value)" placeholder="/">
                            </div>
                            <div class="col-md-6 mb-2">
                                <label class="form-label">Host</label>
                                <input type="text" class="form-control" value="${node.config['ws-opts']?.headers?.Host || ''}"
                                       onchange="updateWSConfig('${node.id}', 'host', this.value)" placeholder="example.com">
                            </div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        default:
            return '<p class="text-muted">未知节点类型</p>';
    }
}
